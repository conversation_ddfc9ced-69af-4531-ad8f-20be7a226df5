import { useCallback, useMemo } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { toast } from 'react-toastify';

import auth from 'modules/auth';

import { ModalCriarVariacao } from 'pages/EntradaMercadoria/Importacao/Continuar/VincularProdutos/ModalVincularProduto/components/ModalCriarVariacao';
import { ModalCadastrarCor } from 'pages/EntradaMercadoria/ModalCadastrarCor';
import { ModalCadastrarProduto } from 'pages/EntradaMercadoria/ModalCadastrarProduto';
import { ModalCadastrarTamanho } from 'pages/EntradaMercadoria/ModalCadastrarTamanho';

import { ModalGradeTamanhos } from 'components/update/Modal/ModalGradeTamanhos';

import OptionType from 'types/optionType';

import TipoProdutoEnum from 'constants/enum/tipoProduto';
import ConstanteFuncionalidades from 'constants/permissoes';

import { ProdutoService } from '../services/produtoService';
import { useModalAdicionarProdutoStore } from '../store/modalAdicionarProdutoStore';
import {
  FormData,
  ModalAdicionarProdutoResponse,
  ProdutoResponse,
  EntradaMercadoriaAdicionarItens,
} from '../validationForm';

export const useModalAdicionarProdutoRefatorado = (
  formMethods: UseFormReturn<FormData>,
  onResolve: (result?: ModalAdicionarProdutoResponse | undefined) => void,
  fecharModal: () => void
) => {
  // Store Zustand
  const {
    isLoading,
    isLoadingProduto,
    produtoSelecionado,
    coresDoProduto,
    tamanhosDoProduto,
    listaVariacoes,
    totalRegistros,
    casasDecimaisQuantidade,
    casasDecimaisValor,
    entradaRateiaIcmsSt,
    setIsLoading,
    setIsLoadingProduto,
    setProdutoSelecionado,
    setCoresDoProduto,
    setTamanhosDoProduto,
    adicionarVariacao,
    removerVariacao,
    setListaVariacoes,
    limparListaVariacoes,
    setTotalRegistros,
    resetarEstado,
  } = useModalAdicionarProdutoStore();

  // Form methods
  const { watch, setValue, setFocus } = formMethods;
  const {
    listaTamanhoIdQuantidade,
    cor: corWatch,
    produto: produtoWatch,
    pesquisarPorLeitor: pesquisarPorLeitorWatch,
  } = watch();

  // Permissões
  const { permitido: temPermissaoCadastrarProduto } = auth.possuiPermissao(
    ConstanteFuncionalidades.PRODUTO_CADASTRAR
  );
  const { permitido: temPermissaoCadastrarCor } = auth.possuiPermissao(
    ConstanteFuncionalidades.COR_CADASTRAR
  );
  const { permitido: temPermissaoCadastrarTamanho } = auth.possuiPermissao(
    ConstanteFuncionalidades.TAMANHO_CADASTRAR
  );

  // Computed values
  const produtoTipoSimples = useMemo(
    () => produtoSelecionado?.tipoProduto === TipoProdutoEnum.PRODUTO_SIMPLES,
    [produtoSelecionado?.tipoProduto]
  );

  const produtoTipoVariacao = useMemo(
    () => produtoSelecionado?.tipoProduto === TipoProdutoEnum.PRODUTO_VARIACAO,
    [produtoSelecionado?.tipoProduto]
  );

  const produtoDeVolumeUnitario = useMemo(
    () => produtoSelecionado?.volumeUnitario,
    [produtoSelecionado?.volumeUnitario]
  );

  const produtoTemCores = useMemo(
    () => coresDoProduto?.length > 0,
    [coresDoProduto]
  );
  const produtoTemTamanhos = useMemo(
    () => tamanhosDoProduto?.length > 0,
    [tamanhosDoProduto]
  );

  const corEscolhida = corWatch;
  const tamanhoEscolhido = useMemo(
    () =>
      (listaTamanhoIdQuantidade || [])?.length > 0 &&
      listaTamanhoIdQuantidade &&
      listaTamanhoIdQuantidade[0]?.tamanho,
    [listaTamanhoIdQuantidade]
  );

  const temGradeLancada = useMemo(
    () => (listaTamanhoIdQuantidade || []).length > 1,
    [listaTamanhoIdQuantidade]
  );

  // Validação para confirmar
  const podeConfirmar = useMemo(() => {
    if (produtoTipoSimples) {
      return true;
    }

    if (produtoTemCores && produtoTemTamanhos) {
      return !!corEscolhida && !!tamanhoEscolhido;
    }

    if (produtoTemCores) {
      return !!corEscolhida;
    }

    if (produtoTemTamanhos) {
      return !!tamanhoEscolhido;
    }

    return false;
  }, [
    corEscolhida,
    produtoTemCores,
    produtoTemTamanhos,
    produtoTipoSimples,
    tamanhoEscolhido,
  ]);

  // Validação de quantidade
  const quantidadeMaiorQueZero = useCallback(() => {
    const listaProdutos = watch('listaTamanhoIdQuantidade') || [];
    return listaProdutos?.every((element) => element.quantidade > 0);
  }, [watch]);

  // Buscar produto
  const buscarProduto = useCallback(
    async (inputValue: string, currentPage = 1, pageSize = 10) => {
      setIsLoadingProduto(true);

      try {
        const result = await ProdutoService.buscarProduto({
          inputValue,
          currentPage,
          pageSize,
          orderColumn: 'nome',
          orderDirection: 'asc',
        });

        setTotalRegistros(result.totalRegistros);
        return result.produtos;
      } finally {
        setIsLoadingProduto(false);
      }
    },
    [setIsLoadingProduto, setTotalRegistros]
  );

  // Validar pesquisa por código
  const validarPesquisaPorCodigo = useCallback((inputValue: string) => {
    return ProdutoService.validarPesquisaPorCodigo(inputValue);
  }, []);

  // Manipular seleção de produto
  const onChangeSelectProduto = useCallback(
    (opcao: OptionType | null) => {
      if (opcao?.value) {
        setProdutoSelecionado(opcao.value);
        setValue('valorUnitario', opcao.value.precoCompra || 0);
      } else {
        setProdutoSelecionado(null);
        setValue('valorUnitario', 0);
      }

      // Limpar campos relacionados
      setValue('cor', null);
      setValue('listaTamanhoIdQuantidade', []);
      limparListaVariacoes();
    },
    [setProdutoSelecionado, setValue, limparListaVariacoes]
  );

  // Cadastrar cor
  const handleCadastrarCor = useCallback(
    async (descricaoCor: string) => {
      if (!produtoSelecionado?.id || !temPermissaoCadastrarCor) {
        toast.warning('Sem permissão para cadastrar cor');
        return null;
      }

      setIsLoading(true);
      try {
        const novaCor = await ProdutoService.cadastrarCor(
          produtoSelecionado.id,
          descricaoCor
        );

        if (novaCor) {
          const novasCores = [...coresDoProduto, novaCor];
          setCoresDoProduto(novasCores);
          return novaCor;
        }

        return null;
      } finally {
        setIsLoading(false);
      }
    },
    [
      produtoSelecionado?.id,
      temPermissaoCadastrarCor,
      coresDoProduto,
      setCoresDoProduto,
      setIsLoading,
    ]
  );

  // Cadastrar tamanho
  const handleCadastrarTamanho = useCallback(
    async (descricaoTamanho: string) => {
      if (!produtoSelecionado?.id || !temPermissaoCadastrarTamanho) {
        toast.warning('Sem permissão para cadastrar tamanho');
        return null;
      }

      setIsLoading(true);
      try {
        const novoTamanho = await ProdutoService.cadastrarTamanho(
          produtoSelecionado.id,
          descricaoTamanho
        );

        if (novoTamanho) {
          const novosTamanhos = [...tamanhosDoProduto, novoTamanho];
          setTamanhosDoProduto(novosTamanhos);
          return novoTamanho;
        }

        return null;
      } finally {
        setIsLoading(false);
      }
    },
    [
      produtoSelecionado?.id,
      temPermissaoCadastrarTamanho,
      tamanhosDoProduto,
      setTamanhosDoProduto,
      setIsLoading,
    ]
  );

  // Cadastrar produto
  const handleCadastrarProduto = useCallback(
    async (nomeProduto: string) => {
      if (!temPermissaoCadastrarProduto) {
        toast.warning('Sem permissão para cadastrar produto');
        return null;
      }

      setIsLoading(true);
      try {
        const novoProduto = await ProdutoService.cadastrarProduto(nomeProduto);
        return novoProduto;
      } finally {
        setIsLoading(false);
      }
    },
    [temPermissaoCadastrarProduto, setIsLoading]
  );

  return {
    // Estados
    isLoading,
    isLoadingProduto,
    totalRegistros,
    temGradeLancada,
    listaVariacoes,
    produtoDeVolumeUnitario,
    produtoTipoVariacao,
    produtoTemCores,
    produtoTemTamanhos,
    corEscolhida,
    podeConfirmar,
    produtoSelecionado,
    coresDoProduto,
    tamanhosDoProduto,
    pesquisarPorLeitorWatch,

    // Funções
    buscarProduto,
    quantidadeMaiorQueZero,
    validarPesquisaPorCodigo,
    onChangeSelectProduto,
    handleCadastrarCor,
    handleCadastrarTamanho,
    handleCadastrarProduto,

    // Store actions
    setListaVariacoes,
    setValue,
    resetarEstado,
  };
};
