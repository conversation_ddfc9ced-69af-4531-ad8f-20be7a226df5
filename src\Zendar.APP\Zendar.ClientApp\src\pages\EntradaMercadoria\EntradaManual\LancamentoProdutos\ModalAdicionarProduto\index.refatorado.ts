// Exportações da versão refatorada do ModalAdicionarProduto

// Store Zustand
export { useModalAdicionarProdutoStore } from './store/modalAdicionarProdutoStore';

// Serviços
export { ProdutoService } from './services/produtoService';

// Hooks refatorados
export { useModalAdicionarProdutoRefatorado } from './hooks/useModalAdicionarProdutoRefatorado';

// Componentes especializados
export { SeletorProduto } from './components/SeletorProduto';
export { SecaoVariacoes } from './components/SecaoVariacoes';

// Schemas de validação
export {
  formDataSchema,
  createConditionalSchema,
  variacaoSchema,
  produtoResponseSchema,
  formDataResolver,
  createConditionalResolver,
  validarQuantidadeTotal,
  validarVariacaoCompleta,
} from './schemas/validationSchemas';

// Componente principal refatorado
export { ModalAdicionarProdutoRefatorado } from './ModalAdicionarProdutoRefatorado';

// Re-exportar tipos existentes
export type {
  ModalAdicionarProdutoProps,
  ModalAdicionarProdutoResponse,
  FormData,
  ProdutoOptionProps,
  ProdutoResponse,
  EntradaMercadoriaAdicionarItens,
  Cor,
  Tamanho,
  TamanhoQuantidade,
  Variacao,
  Produto,
} from './validationForm';
