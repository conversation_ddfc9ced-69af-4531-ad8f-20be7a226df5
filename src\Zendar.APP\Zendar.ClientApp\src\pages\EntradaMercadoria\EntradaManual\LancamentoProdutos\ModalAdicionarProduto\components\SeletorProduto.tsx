import { Box, Flex, Icon, Text, useMediaQuery } from '@chakra-ui/react';
import React from 'react';
import { useFormContext } from 'react-hook-form';

import { ModalConsultaProdutosEntradaMercadoria } from 'pages/EntradaMercadoria/components/ConsultarProdutos';

import CreatableSelectVirtualized from 'components/PDV/Select/CreatableSelectVirtualized';

import OptionType from 'types/optionType';

import { LupaIcon } from 'icons';

import { FormData, ProdutoOptionProps } from '../validationForm';

interface SeletorProdutoProps {
  buscarProduto: (
    inputValue: string,
    currentPage?: number,
    pageSize?: number
  ) => Promise<OptionType<ProdutoOptionProps>[]>;
  isLoadingProduto: boolean;
  pesquisarPorLeitorWatch: boolean;
  validarPesquisaPorCodigo: (inputValue: string) => boolean;
  handleCadastrarProduto: (
    inputValue: string
  ) => Promise<OptionType<ProdutoOptionProps> | null>;
  onChangeSelectProduto: (opcao: OptionType<ProdutoOptionProps> | null) => void;
  totalRegistros: number;
  casasDecimaisQuantidade: number;
}

export const SeletorProduto: React.FC<SeletorProdutoProps> = ({
  buscarProduto,
  isLoadingProduto,
  pesquisarPorLeitorWatch,
  validarPesquisaPorCodigo,
  handleCadastrarProduto,
  onChangeSelectProduto,
  totalRegistros,
  casasDecimaisQuantidade,
}) => {
  const [isLargerThan700] = useMediaQuery('(min-width: 700px)');
  const { setValue } = useFormContext<FormData>();

  const handleConsultarProdutos = async () => {
    const response = await ModalConsultaProdutosEntradaMercadoria({
      casasDecimais: {
        casasDecimaisQuantidade,
      },
    });

    const produtoOpcao = {
      label: response.nome,
      value: {
        ...response,
        coresOptions: [],
        tamanhosOptions: [],
      } as ProdutoOptionProps,
    };

    setValue('produto', produtoOpcao);
    onChangeSelectProduto(produtoOpcao);
  };

  return (
    <Flex
      justify="space-between"
      width="full"
      gap={6}
      direction={{ base: 'column', md: 'row' }}
      align={{ base: 'end', md: 'center' }}
    >
      <Box width={{ base: 'full', lg: '60%' }}>
        <CreatableSelectVirtualized
          id="produto"
          name="produto"
          placeholder={
            isLargerThan700
              ? 'Digite o nome do produto ou utilize um leitor de códigos'
              : 'Digite o nome do produto ou utilize um leitor'
          }
          handleGetOptions={buscarProduto}
          isLoading={isLoadingProduto}
          creatableButtonShow={!pesquisarPorLeitorWatch}
          creatableInputTextPreffix="Cadastrar o produto"
          handleCreateOption={(inputValue) => {
            if (!validarPesquisaPorCodigo(inputValue)) {
              return handleCadastrarProduto(inputValue);
            }
          }}
          onChangeSelect={onChangeSelectProduto}
          required
          asControlledByObject
          isClearable
          autoFocus
          closeMenuOnSelect
          totalRegistros={totalRegistros}
        />
      </Box>

      <Flex
        gap="6px"
        px={2}
        cursor="pointer"
        width="min"
        align="center"
        onClick={handleConsultarProdutos}
      >
        <Icon as={LupaIcon} fontSize="20px" color="white" />
        <Text color="white" textDecor="underline" fontSize="16px" width="max">
          Consultar produtos
        </Text>
      </Flex>
    </Flex>
  );
};
