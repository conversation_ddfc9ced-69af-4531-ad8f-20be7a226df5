import * as yup from 'yup';
import { yupResolver as yupResolverInstance } from '@hookform/resolvers/yup';

import ConstanteMensagemValidacao from 'constants/mensagensValidacoes';

// Schema base para OptionType
const optionTypeSchema = yup.object().shape({
  value: yup.string().required(ConstanteMensagemValidacao.CAMPO_OBRIGATORIO),
  label: yup.string().required(ConstanteMensagemValidacao.CAMPO_OBRIGATORIO),
});

// Schema para produto
const produtoSchema = yup.object().shape({
  id: yup.string().required(ConstanteMensagemValidacao.CAMPO_OBRIGATORIO),
  nome: yup.string().required(ConstanteMensagemValidacao.CAMPO_OBRIGATORIO),
  tipoProduto: yup.number().required(ConstanteMensagemValidacao.CAMPO_OBRIGATORIO),
  volumeUnitario: yup.boolean().required(),
  referencia: yup.string().nullable(),
  precoCompra: yup.number().min(0, 'Preço deve ser maior ou igual a zero'),
  codigoGTINEAN: yup.string().nullable(),
  coresOptions: yup.array().of(optionTypeSchema).default([]),
  tamanhosOptions: yup.array().of(optionTypeSchema).default([]),
});

// Schema para tamanho e quantidade
const tamanhoQuantidadeSchema = yup.object().shape({
  tamanho: optionTypeSchema.nullable(),
  quantidade: yup
    .number()
    .required(ConstanteMensagemValidacao.CAMPO_OBRIGATORIO)
    .min(0.001, 'Quantidade deve ser maior que zero')
    .typeError(ConstanteMensagemValidacao.CAMPO_OBRIGATORIO),
});

// Schema principal do formulário
export const formDataSchema = yup.object().shape({
  produto: yup
    .object()
    .shape({
      value: produtoSchema.required(ConstanteMensagemValidacao.CAMPO_OBRIGATORIO),
      label: yup.string().required(ConstanteMensagemValidacao.CAMPO_OBRIGATORIO),
    })
    .nullable()
    .required(ConstanteMensagemValidacao.CAMPO_OBRIGATORIO),
    
  cor: optionTypeSchema.nullable(),
  
  listaTamanhoIdQuantidade: yup
    .array()
    .of(tamanhoQuantidadeSchema)
    .min(1, 'Pelo menos um tamanho deve ser informado')
    .test('quantidade-valida', 'Todas as quantidades devem ser maiores que zero', (value) => {
      if (!value || value.length === 0) return false;
      return value.every((item) => item.quantidade > 0);
    }),
    
  valorUnitario: yup
    .number()
    .required(ConstanteMensagemValidacao.CAMPO_OBRIGATORIO)
    .min(0, 'Valor unitário deve ser maior ou igual a zero')
    .typeError(ConstanteMensagemValidacao.CAMPO_OBRIGATORIO),
    
  ipi: yup
    .number()
    .min(0, 'IPI deve ser maior ou igual a zero')
    .default(0)
    .typeError('IPI deve ser um número válido'),
    
  icmsSt: yup
    .number()
    .min(0, 'ICMS ST deve ser maior ou igual a zero')
    .default(0)
    .typeError('ICMS ST deve ser um número válido'),
    
  fcpSt: yup
    .number()
    .min(0, 'FCP ST deve ser maior ou igual a zero')
    .default(0)
    .typeError('FCP ST deve ser um número válido'),
    
  custoAdicional: yup
    .number()
    .min(0, 'Custo adicional deve ser maior ou igual a zero')
    .default(0)
    .typeError('Custo adicional deve ser um número válido'),
    
  pesquisarPorLeitor: yup.boolean().default(false),
});

// Schema condicional baseado no tipo de produto
export const createConditionalSchema = (produtoTipoVariacao: boolean, produtoTemCores: boolean, produtoTemTamanhos: boolean) => {
  let conditionalSchema = formDataSchema;

  if (produtoTipoVariacao) {
    // Para produtos com variação, validações específicas
    if (produtoTemCores) {
      conditionalSchema = conditionalSchema.shape({
        cor: optionTypeSchema.required('Cor é obrigatória para produtos com variação'),
      });
    }

    if (produtoTemTamanhos) {
      conditionalSchema = conditionalSchema.shape({
        listaTamanhoIdQuantidade: yup
          .array()
          .of(
            yup.object().shape({
              tamanho: optionTypeSchema.required('Tamanho é obrigatório'),
              quantidade: yup
                .number()
                .required(ConstanteMensagemValidacao.CAMPO_OBRIGATORIO)
                .min(0.001, 'Quantidade deve ser maior que zero'),
            })
          )
          .min(1, 'Pelo menos um tamanho deve ser selecionado'),
      });
    }
  } else {
    // Para produtos simples, apenas quantidade é obrigatória
    conditionalSchema = conditionalSchema.shape({
      listaTamanhoIdQuantidade: yup
        .array()
        .of(tamanhoQuantidadeSchema)
        .min(1, 'Quantidade é obrigatória')
        .max(1, 'Produtos simples devem ter apenas uma entrada de quantidade'),
    });
  }

  return conditionalSchema;
};

// Schema para validação de variação individual
export const variacaoSchema = yup.object().shape({
  corId: yup.string().nullable(),
  tamanhoId: yup.string().nullable(),
  quantidade: yup
    .number()
    .required(ConstanteMensagemValidacao.CAMPO_OBRIGATORIO)
    .min(0.001, 'Quantidade deve ser maior que zero'),
});

// Schema para produto response (dados enviados para API)
export const produtoResponseSchema = yup.object().shape({
  valorUnitarioEntrada: yup
    .number()
    .required(ConstanteMensagemValidacao.CAMPO_OBRIGATORIO)
    .min(0, 'Valor unitário deve ser maior ou igual a zero'),
    
  valorIpi: yup
    .number()
    .min(0, 'Valor IPI deve ser maior ou igual a zero')
    .default(0),
    
  valorIcmsSt: yup
    .number()
    .min(0, 'Valor ICMS ST deve ser maior ou igual a zero')
    .default(0),
    
  valorFcpSt: yup
    .number()
    .min(0, 'Valor FCP ST deve ser maior ou igual a zero')
    .default(0),
    
  custoAdicional: yup
    .number()
    .min(0, 'Custo adicional deve ser maior ou igual a zero')
    .default(0),
    
  produtoId: yup
    .string()
    .required(ConstanteMensagemValidacao.CAMPO_OBRIGATORIO),
    
  variacoes: yup
    .array()
    .of(variacaoSchema)
    .min(1, 'Pelo menos uma variação deve ser informada'),
});

// Resolvers para react-hook-form
export const formDataResolver = yupResolverInstance(formDataSchema);

export const createConditionalResolver = (
  produtoTipoVariacao: boolean,
  produtoTemCores: boolean,
  produtoTemTamanhos: boolean
) => {
  const schema = createConditionalSchema(produtoTipoVariacao, produtoTemCores, produtoTemTamanhos);
  return yupResolverInstance(schema);
};

// Funções de validação customizadas
export const validarQuantidadeTotal = (listaTamanhoIdQuantidade: any[]): boolean => {
  if (!listaTamanhoIdQuantidade || listaTamanhoIdQuantidade.length === 0) {
    return false;
  }
  
  const quantidadeTotal = listaTamanhoIdQuantidade.reduce(
    (acc, curr) => acc + (curr.quantidade || 0),
    0
  );
  
  return quantidadeTotal > 0;
};

export const validarVariacaoCompleta = (
  produtoTipoVariacao: boolean,
  produtoTemCores: boolean,
  produtoTemTamanhos: boolean,
  cor: any,
  listaTamanhoIdQuantidade: any[]
): boolean => {
  if (!produtoTipoVariacao) {
    return validarQuantidadeTotal(listaTamanhoIdQuantidade);
  }

  let isValid = true;

  if (produtoTemCores && !cor) {
    isValid = false;
  }

  if (produtoTemTamanhos && (!listaTamanhoIdQuantidade || listaTamanhoIdQuantidade.length === 0)) {
    isValid = false;
  }

  if (produtoTemTamanhos) {
    const temTamanhoSelecionado = listaTamanhoIdQuantidade.some(
      (item) => item.tamanho && item.quantidade > 0
    );
    if (!temTamanhoSelecionado) {
      isValid = false;
    }
  }

  return isValid && validarQuantidadeTotal(listaTamanhoIdQuantidade);
};
