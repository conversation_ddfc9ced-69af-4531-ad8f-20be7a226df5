import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';

import {
  ProdutoOptionProps,
  EntradaMercadoriaAdicionarItens,
  FormData,
  ProdutoResponse,
  Cor,
  Tamanho,
} from '../validationForm';

interface ModalAdicionarProdutoState {
  // Estados de loading
  isLoading: boolean;
  isLoadingProduto: boolean;

  // Dados do produto
  produtoSelecionado: ProdutoOptionProps | null;
  coresDoProduto: Cor[];
  tamanhosDoProduto: Tamanho[];

  // Lista de variações adicionadas
  listaVariacoes: EntradaMercadoriaAdicionarItens[];

  // Controles de UI
  totalRegistros: number;

  // Configurações
  casasDecimaisQuantidade: number;
  casasDecimaisValor: number;
  entradaRateiaIcmsSt: boolean;
}

interface ModalAdicionarProdutoActions {
  // Ações de loading
  setIsLoading: (loading: boolean) => void;
  setIsLoadingProduto: (loading: boolean) => void;

  // Ações do produto
  setProdutoSelecionado: (produto: ProdutoOptionProps | null) => void;
  setCoresDoProduto: (cores: Cor[]) => void;
  setTamanhosDoProduto: (tamanhos: Tamanho[]) => void;

  // Ações da lista de variações
  adicionarVariacao: (variacao: EntradaMercadoriaAdicionarItens) => void;
  removerVariacao: (index: number) => void;
  setListaVariacoes: (variacoes: EntradaMercadoriaAdicionarItens[]) => void;
  limparListaVariacoes: () => void;

  // Ações de configuração
  setTotalRegistros: (total: number) => void;
  setConfiguracoes: (config: {
    casasDecimaisQuantidade: number;
    casasDecimaisValor: number;
    entradaRateiaIcmsSt: boolean;
  }) => void;

  // Ação para resetar o estado
  resetarEstado: () => void;
}

type ModalAdicionarProdutoStore = ModalAdicionarProdutoState &
  ModalAdicionarProdutoActions;

const estadoInicial: ModalAdicionarProdutoState = {
  isLoading: false,
  isLoadingProduto: false,
  produtoSelecionado: null,
  coresDoProduto: [],
  tamanhosDoProduto: [],
  listaVariacoes: [],
  totalRegistros: 0,
  casasDecimaisQuantidade: 2,
  casasDecimaisValor: 2,
  entradaRateiaIcmsSt: false,
};

export const useModalAdicionarProdutoStore =
  create<ModalAdicionarProdutoStore>()(
    immer((set) => ({
      ...estadoInicial,

      // Ações de loading
      setIsLoading: (loading) =>
        set((state) => {
          state.isLoading = loading;
        }),

      setIsLoadingProduto: (loading) =>
        set((state) => {
          state.isLoadingProduto = loading;
        }),

      // Ações do produto
      setProdutoSelecionado: (produto) =>
        set((state) => {
          state.produtoSelecionado = produto;
          if (produto) {
            state.coresDoProduto = produto.coresOptions || [];
            state.tamanhosDoProduto = produto.tamanhosOptions || [];
          } else {
            state.coresDoProduto = [];
            state.tamanhosDoProduto = [];
          }
        }),

      setCoresDoProduto: (cores) =>
        set((state) => {
          state.coresDoProduto = cores;
        }),

      setTamanhosDoProduto: (tamanhos) =>
        set((state) => {
          state.tamanhosDoProduto = tamanhos;
        }),

      // Ações da lista de variações
      adicionarVariacao: (variacao) =>
        set((state) => {
          state.listaVariacoes.push(variacao);
        }),

      removerVariacao: (index) =>
        set((state) => {
          state.listaVariacoes.splice(index, 1);
        }),

      setListaVariacoes: (variacoes) =>
        set((state) => {
          state.listaVariacoes = variacoes;
        }),

      limparListaVariacoes: () =>
        set((state) => {
          state.listaVariacoes = [];
        }),

      // Ações de configuração
      setTotalRegistros: (total) =>
        set((state) => {
          state.totalRegistros = total;
        }),

      setConfiguracoes: (config) =>
        set((state) => {
          state.casasDecimaisQuantidade = config.casasDecimaisQuantidade;
          state.casasDecimaisValor = config.casasDecimaisValor;
          state.entradaRateiaIcmsSt = config.entradaRateiaIcmsSt;
        }),

      // Ação para resetar o estado
      resetarEstado: () =>
        set((state) => {
          Object.assign(state, estadoInicial);
        }),
    }))
  );
