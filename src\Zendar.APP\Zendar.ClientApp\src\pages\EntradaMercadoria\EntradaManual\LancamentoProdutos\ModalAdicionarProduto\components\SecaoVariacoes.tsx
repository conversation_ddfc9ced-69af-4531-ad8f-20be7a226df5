import { Form<PERSON>abel, <PERSON><PERSON><PERSON><PERSON>, But<PERSON> } from '@chakra-ui/react';
import React from 'react';

import { MenuSelect } from 'pages/EntradaMercadoria/components/MenuSelect';

import CreatableSelect from 'components/PDV/Select/CreatableSelect';
import { chakraComponents } from 'components/PDV/Select/ReactSelectIntegracao';
import { SimpleGridForm } from 'components/update/Form/SimpleGridForm';

import OptionType from 'types/optionType';

import { Quantidade, Tamanho } from './index';

interface SecaoVariacoesProps {
  produtoTipoVariacao: boolean;
  produtoTemCores: boolean;
  produtoTemTamanhos: boolean;
  coresDoProduto: OptionType[];
  tamanhosDoProduto: OptionType[];
  handleCadastrarCor: (descricao: string) => Promise<OptionType | null>;
  handleCadastrarTamanho: (descricao: string) => Promise<OptionType | null>;
  isLoadingProduto: boolean;
  temGradeLancada: boolean;
  corEscolhida: OptionType | null;
  handleAbrirModalEscolherGradeTamanhos: () => void;
  abrirModalVariacoes: () => void;
  casasDecimaisQuantidade: number;
  produtoDeVolumeUnitario: boolean;
  quantidadeTotal?: number;
  adicionarVariacaoNaLista: () => void;
  podeConfirmar: boolean;
  quantidadeMaiorQueZero: () => boolean;
}

export const SecaoVariacoes: React.FC<SecaoVariacoesProps> = ({
  produtoTipoVariacao,
  produtoTemCores,
  produtoTemTamanhos,
  coresDoProduto,
  tamanhosDoProduto,
  handleCadastrarCor,
  handleCadastrarTamanho,
  isLoadingProduto,
  temGradeLancada,
  corEscolhida,
  handleAbrirModalEscolherGradeTamanhos,
  abrirModalVariacoes,
  casasDecimaisQuantidade,
  produtoDeVolumeUnitario,
  quantidadeTotal,
  adicionarVariacaoNaLista,
  podeConfirmar,
  quantidadeMaiorQueZero,
}) => {
  if (!produtoTipoVariacao) {
    return null;
  }

  return (
    <>
      <FormLabel
        fontSize="14px"
        fontWeight="600"
        color="black"
        mt="19px"
        mb="0px"
      >
        Informe uma variação
      </FormLabel>

      <SimpleGridForm>
        {produtoTemCores && (
          <CreatableSelect
            id="cor"
            name="cor"
            label="Cor"
            placeholder="Selecione uma cor cadastrada no produto"
            creatableInputTextPreffix="Cadastrar a cor"
            colSpan={[12, 12, 4, 4]}
            handleCreateOption={handleCadastrarCor}
            options={coresDoProduto}
            disabled={isLoadingProduto}
            asControlledByObject
            required
            menuPortalTarget={document?.body}
            actionLinkText="Adicionar nova"
            components={{
              ...chakraComponents,
              MenuList: (props: any) => (
                <MenuSelect
                  texto={
                    coresDoProduto?.length > 0
                      ? '**Exibindo apenas cores ativas do produto**'
                      : ''
                  }
                  {...props}
                />
              ),
            }}
            actionLinkOnClick={abrirModalVariacoes}
          />
        )}

        {produtoTemTamanhos && (
          <Tamanho
            temGradeLancada={temGradeLancada}
            tamanhosDoProduto={tamanhosDoProduto}
            handleCadastrarTamanho={handleCadastrarTamanho}
            isLoadingProduto={isLoadingProduto}
            produtoTemCores={produtoTemCores}
            corEscolhida={corEscolhida}
            handleAbrirModalEscolherGradeTamanhos={
              handleAbrirModalEscolherGradeTamanhos
            }
            produtoTemTamanhos={produtoTemTamanhos}
            abrirModalVariacoes={abrirModalVariacoes}
          />
        )}

        <Quantidade
          produtoTemGradeLancada={temGradeLancada}
          casasDecimaisQuantidade={casasDecimaisQuantidade}
          produtoDeVolumeUnitario={produtoDeVolumeUnitario}
          produtoPossuiVariacoes={produtoTipoVariacao}
          quantidade={quantidadeTotal}
        />

        <GridItem colSpan={[12, 12, 2]} mt="18px" ml="0px">
          <Button
            colorScheme="teal"
            borderRadius="full"
            w="full"
            height="36px"
            fontSize="14px"
            minW="176px"
            onClick={adicionarVariacaoNaLista}
            isDisabled={!podeConfirmar || !quantidadeMaiorQueZero()}
            fontWeight="600"
          >
            Adicionar variação
          </Button>
        </GridItem>
      </SimpleGridForm>
    </>
  );
};
