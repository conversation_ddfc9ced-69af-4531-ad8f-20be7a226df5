import {
  ModalContent,
  ModalBody,
  useDisclosure,
  ModalHeader,
  Collapse,
  Flex,
  useMediaQuery,
  GridItem,
} from '@chakra-ui/react';
import React, { useEffect } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { create } from 'react-modal-promise';

import LoadingPadrao from 'components/Layout/Loading/LoadingPadrao';
import ModalPadraoChakra from 'components/PDV/Modal/ModalPadraoChakra';
import { SimpleCard } from 'components/update/Form/SimpleCard';
import { SimpleGridForm } from 'components/update/Form/SimpleGridForm';

import {
  Footer,
  FormularioValoresFiscais,
  ListagemVariacoesAdicionadas,
  Quantidade,
} from './components';
import { SecaoVariacoes } from './components/SecaoVariacoes';
import { SeletorProduto } from './components/SeletorProduto';
import { useModalAdicionarProdutoRefatorado } from './hooks/useModalAdicionarProdutoRefatorado';
import { useModalAdicionarProdutoStore } from './store/modalAdicionarProdutoStore';
import {
  ModalAdicionarProdutoProps,
  ModalAdicionarProdutoResponse,
  FormData,
} from './validationForm';

// Componentes especializados

export const ModalAdicionarProdutoRefatorado = create<
  ModalAdicionarProdutoProps,
  ModalAdicionarProdutoResponse
>(
  ({
    onResolve,
    onReject,
    casasDecimaisQuantidade,
    casasDecimaisValor,
    entradaRateiaIcmsSt,
    adicionarProduto,
    ...rest
  }) => {
    const { isOpen, onClose } = useDisclosure({ defaultIsOpen: true });
    const [isLargerThan900] = useMediaQuery('(min-width: 900px)');

    // Store Zustand
    const { setConfiguracoes, resetarEstado } = useModalAdicionarProdutoStore();

    const fecharModal = () => {
      resetarEstado();
      onReject();
      onClose();
    };

    const formMethods = useForm<FormData>({
      defaultValues: {
        cor: null,
      },
    });

    // Hook refatorado
    const {
      isLoading,
      isLoadingProduto,
      totalRegistros,
      temGradeLancada,
      listaVariacoes,
      produtoDeVolumeUnitario,
      produtoTipoVariacao,
      produtoTemCores,
      produtoTemTamanhos,
      corEscolhida,
      podeConfirmar,
      produtoSelecionado,
      coresDoProduto,
      tamanhosDoProduto,
      pesquisarPorLeitorWatch,
      buscarProduto,
      quantidadeMaiorQueZero,
      validarPesquisaPorCodigo,
      onChangeSelectProduto,
      handleCadastrarCor,
      handleCadastrarTamanho,
      handleCadastrarProduto,
      setListaVariacoes,
      setValue,
    } = useModalAdicionarProdutoRefatorado(formMethods, onResolve, fecharModal);

    // Configurar store na inicialização
    useEffect(() => {
      setConfiguracoes({
        casasDecimaisQuantidade,
        casasDecimaisValor,
        entradaRateiaIcmsSt,
      });
    }, [
      casasDecimaisQuantidade,
      casasDecimaisValor,
      entradaRateiaIcmsSt,
      setConfiguracoes,
    ]);

    // Cleanup ao desmontar
    useEffect(() => {
      return () => {
        resetarEstado();
      };
    }, [resetarEstado]);

    // Funções de ação (implementar conforme necessário)
    const handleConfirmarAdicionarNovo = () => {
      // Implementar lógica
    };

    const handleConfirmarSair = () => {
      // Implementar lógica
    };

    const adicionarVariacaoNaLista = () => {
      // Implementar lógica
    };

    const handleAbrirModalEscolherGradeTamanhos = () => {
      // Implementar lógica
    };

    const abrirModalVariacoes = () => {
      // Implementar lógica
    };

    const limparValoresFiscaisVoltarValorPrecoCompra = () => {
      setValue('ipi', 0);
      setValue('icmsSt', 0);
      setValue('fcpSt', 0);
      setValue('custoAdicional', 0);
      setValue('valorUnitario', produtoSelecionado?.precoCompra || 0);
    };

    const quantidadeTotal = formMethods
      ?.watch('listaTamanhoIdQuantidade')
      ?.reduce((acc, curr) => acc + curr.quantidade, 0);

    return (
      <ModalPadraoChakra
        isCentered={isLargerThan900}
        size="full"
        {...rest}
        isOpen={isOpen}
        onClose={fecharModal}
      >
        <ModalContent h="unset" bg="gray.100" borderRadius="0px">
          {isLoading && <LoadingPadrao />}

          <ModalHeader
            px={['16px', '40px']}
            py="20px"
            color="violet.500"
            fontWeight="normal"
            fontSize="18px"
          >
            Adicionar Produto
          </ModalHeader>

          <ModalBody px={['16px', '40px']} pt="0px">
            <FormProvider {...formMethods}>
              {/* Seção de busca de produto */}
              <SimpleCard boxShadow="none" bg="violet.500" p="16px">
                <SeletorProduto
                  buscarProduto={buscarProduto}
                  isLoadingProduto={isLoadingProduto}
                  pesquisarPorLeitorWatch={pesquisarPorLeitorWatch}
                  validarPesquisaPorCodigo={validarPesquisaPorCodigo}
                  handleCadastrarProduto={handleCadastrarProduto}
                  onChangeSelectProduto={onChangeSelectProduto}
                  totalRegistros={totalRegistros}
                  casasDecimaisQuantidade={casasDecimaisQuantidade}
                />
              </SimpleCard>

              {/* Seção de variações e detalhes do produto */}
              <Collapse
                in={!!produtoSelecionado && !isLoadingProduto}
                animateOpacity
              >
                <SimpleCard
                  background={produtoTipoVariacao ? 'gray.200' : 'gray.100'}
                  boxShadow="none"
                  p="28px 16px"
                  px={produtoTipoVariacao ? '40px' : '16px'}
                  pb={produtoTipoVariacao ? '48px' : '28px'}
                >
                  <SimpleGridForm>
                    {/* Seção de variações */}
                    <SecaoVariacoes
                      produtoTipoVariacao={produtoTipoVariacao}
                      produtoTemCores={produtoTemCores}
                      produtoTemTamanhos={produtoTemTamanhos}
                      coresDoProduto={coresDoProduto}
                      tamanhosDoProduto={tamanhosDoProduto}
                      handleCadastrarCor={handleCadastrarCor}
                      handleCadastrarTamanho={handleCadastrarTamanho}
                      isLoadingProduto={isLoadingProduto}
                      temGradeLancada={temGradeLancada}
                      corEscolhida={corEscolhida}
                      handleAbrirModalEscolherGradeTamanhos={
                        handleAbrirModalEscolherGradeTamanhos
                      }
                      abrirModalVariacoes={abrirModalVariacoes}
                      casasDecimaisQuantidade={casasDecimaisQuantidade}
                      produtoDeVolumeUnitario={!!produtoDeVolumeUnitario}
                      quantidadeTotal={quantidadeTotal}
                      adicionarVariacaoNaLista={adicionarVariacaoNaLista}
                      podeConfirmar={podeConfirmar}
                      quantidadeMaiorQueZero={quantidadeMaiorQueZero}
                    />

                    {/* Quantidade para produtos simples */}
                    {!produtoTipoVariacao && (
                      <Quantidade
                        produtoTemGradeLancada={temGradeLancada}
                        casasDecimaisQuantidade={casasDecimaisQuantidade}
                        produtoDeVolumeUnitario={!!produtoDeVolumeUnitario}
                        produtoPossuiVariacoes={produtoTipoVariacao}
                        quantidade={quantidadeTotal}
                      />
                    )}
                  </SimpleGridForm>

                  {/* Valores fiscais e listagem de variações */}
                  <SimpleGridForm mt="20px" columns={[12, 12, 12, 12]}>
                    <FormularioValoresFiscais
                      entradaRateiaIcmsSt={entradaRateiaIcmsSt}
                      casasDecimaisValor={casasDecimaisValor}
                      listaJaPossuiProdutoAdicionado={!!listaVariacoes?.length}
                    />

                    <ListagemVariacoesAdicionadas
                      listaVariacoes={listaVariacoes}
                      setListaVariacoes={setListaVariacoes}
                      casasDecimaisQuantidade={casasDecimaisQuantidade}
                      produtoTemCores={produtoTemCores}
                      produtoTemTamanhos={produtoTemTamanhos}
                      limparValoresFiscaisVoltarValorPrecoCompra={
                        limparValoresFiscaisVoltarValorPrecoCompra
                      }
                    />
                  </SimpleGridForm>
                </SimpleCard>
              </Collapse>
            </FormProvider>
          </ModalBody>

          <Footer
            onReject={onReject}
            onClose={onClose}
            handleConfirmarSair={handleConfirmarSair}
            handleConfirmarAdicionarNovo={handleConfirmarAdicionarNovo}
            isDisabledConfirmar={
              listaVariacoes?.length > 0
                ? false
                : !podeConfirmar || !quantidadeMaiorQueZero()
            }
          />
        </ModalContent>
      </ModalPadraoChakra>
    );
  }
);
