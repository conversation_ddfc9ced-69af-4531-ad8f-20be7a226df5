import { toast } from 'react-toastify';

import { formatQueryPagegTable } from 'helpers/format/formatQueryParams';
import {
  checarPesquisaPorCodigoBarras,
  checarPesquisaPorGtinEan,
  checarPesquisaPorSKU,
} from 'helpers/validation/checarCodigo';

import api, { ResponseApi } from 'services/api';

import { GridPaginadaRetorno } from 'components/Grid/Paginacao';
import { PaginationData } from 'components/update/Pagination';

import OptionType from 'types/optionType';

import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';
import StatusConsultaEnum from 'constants/enum/statusConsulta';

import {
  ProdutoOptionProps,
  ProdutoResponse,
  ProdutoCoresProps,
  ProdutoTamanhoProps,
  NewOption,
  TipoCadastro,
} from '../validationForm';

export interface BuscarProdutoParams extends PaginationData {
  inputValue: string;
}

export interface BuscarProdutoResponse {
  produtos: OptionType<ProdutoOptionProps>[];
  totalRegistros: number;
}

export class ProdutoService {
  /**
   * Busca produtos com base no input do usuário
   */
  static async buscarProduto(
    params: BuscarProdutoParams
  ): Promise<BuscarProdutoResponse> {
    try {
      const { inputValue, ...paginationParams } = params;

      const response = await api.get<
        void,
        ResponseApi<GridPaginadaRetorno<ProdutoOptionProps>>
      >(
        formatQueryPagegTable(
          ConstanteEnderecoWebservice.ENTRADA_MERCADORIA_BUSCAR_PRODUTOS,
          paginationParams
        ),
        {
          params: {
            filtro: inputValue,
            status: StatusConsultaEnum.ATIVOS,
          },
        }
      );

      if (response?.sucesso && response.dados) {
        const produtos = response.dados.registros.map((produto) => ({
          label: produto.nome,
          value: produto,
        }));

        return {
          produtos,
          totalRegistros: response.dados.totalRegistros,
        };
      }

      return {
        produtos: [],
        totalRegistros: 0,
      };
    } catch (error) {
      console.error('Erro ao buscar produtos:', error);
      toast.error('Erro ao buscar produtos');
      return {
        produtos: [],
        totalRegistros: 0,
      };
    }
  }

  /**
   * Valida se a pesquisa é por código (SKU, GTIN/EAN ou código de barras)
   */
  static validarPesquisaPorCodigo(inputValue: string): boolean {
    return (
      checarPesquisaPorSKU(inputValue) ||
      checarPesquisaPorGtinEan(inputValue) ||
      checarPesquisaPorCodigoBarras(inputValue)
    );
  }

  /**
   * Cadastra uma nova cor para o produto
   */
  static async cadastrarCor(
    produtoId: string,
    descricaoCor: string
  ): Promise<OptionType | null> {
    try {
      const response = await api.post<void, ResponseApi<ProdutoCoresProps>>(
        ConstanteEnderecoWebservice.PRODUTO_COR_CADASTRAR,
        {
          produtoId,
          cor: {
            descricao: descricaoCor,
          },
        }
      );

      if (response?.sucesso && response.dados) {
        return {
          label: response.dados.cor.descricao,
          value: response.dados.id,
        };
      }

      if (response?.avisos) {
        response.avisos.forEach((aviso: string) => toast.warning(aviso));
      }

      return null;
    } catch (error) {
      console.error('Erro ao cadastrar cor:', error);
      toast.error('Erro ao cadastrar cor');
      return null;
    }
  }

  /**
   * Cadastra um novo tamanho para o produto
   */
  static async cadastrarTamanho(
    produtoId: string,
    descricaoTamanho: string
  ): Promise<OptionType | null> {
    try {
      const response = await api.post<void, ResponseApi<ProdutoTamanhoProps>>(
        ConstanteEnderecoWebservice.PRODUTO_TAMANHO_CADASTRAR,
        {
          produtoId,
          tamanho: {
            descricao: descricaoTamanho,
          },
        }
      );

      if (response?.sucesso && response.dados) {
        return {
          label: response.dados.descricao,
          value: response.dados.id,
        };
      }

      if (response?.avisos) {
        response.avisos.forEach((aviso: string) => toast.warning(aviso));
      }

      return null;
    } catch (error) {
      console.error('Erro ao cadastrar tamanho:', error);
      toast.error('Erro ao cadastrar tamanho');
      return null;
    }
  }

  /**
   * Cadastra um novo produto
   */
  static async cadastrarProduto(
    nomeProduto: string
  ): Promise<NewOption | null> {
    try {
      const response = await api.post<
        void,
        ResponseApi<{ id: string; nome: string }>
      >(ConstanteEnderecoWebservice.PRODUTO_CADASTRAR, {
        nome: nomeProduto,
        tipoCadastro: TipoCadastro.ENTRADA_MERCADORIA,
      });

      if (response?.sucesso && response.dados) {
        return {
          label: response.dados.nome,
          value: {
            id: response.dados.id,
            nome: response.dados.nome,
            tipoProduto: 1,
            volumeUnitario: false,
            referencia: '',
            precoCompra: 0,
            codigoGTINEAN: null,
            coresOptions: [],
            tamanhosOptions: [],
          },
        };
      }

      if (response?.avisos) {
        response.avisos.forEach((aviso: string) => toast.warning(aviso));
      }

      return null;
    } catch (error) {
      console.error('Erro ao cadastrar produto:', error);
      toast.error('Erro ao cadastrar produto');
      return null;
    }
  }

  /**
   * Adiciona produto na entrada de mercadoria
   */
  static async adicionarProduto(produto: ProdutoResponse): Promise<boolean> {
    try {
      const response = await api.post<void, ResponseApi<any>>(
        ConstanteEnderecoWebservice.ENTRADA_MERCADORIA_ADICIONAR_ITENS,
        produto
      );

      if (response?.avisos) {
        response.avisos.forEach((aviso: string) => toast.warning(aviso));
      }

      if (response?.sucesso) {
        return true;
      }

      return false;
    } catch (error) {
      console.error('Erro ao adicionar produto:', error);
      toast.error('Erro ao adicionar produto');
      return false;
    }
  }
}
